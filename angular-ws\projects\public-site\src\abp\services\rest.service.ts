/**
 * Custom RestService implementation to replace ABP RestService
 * Provides HTTP request functionality with error handling
 */

import { Injectable } from '@angular/core';
import { HttpClient, HttpRequest, HttpHeaders, HttpParams, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { Rest } from '../models/Rest';

@Injectable({
  providedIn: 'root'
})
export class RestService {
  private baseUrl = ''; // You can set a base URL if needed

  constructor(private http: HttpClient) {}

  /**
   * Handle HTTP errors
   */
  handleError(err: any): Observable<any> {
    console.error('HTTP Error:', err);
    
    let errorMessage = 'An error occurred';
    
    if (err instanceof HttpErrorResponse) {
      if (err.error instanceof ErrorEvent) {
        // Client-side error
        errorMessage = `Error: ${err.error.message}`;
      } else {
        // Server-side error
        errorMessage = `Error Code: ${err.status}\nMessage: ${err.message}`;
      }
    }
    
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Make HTTP request
   */
  request<T, R>(
    request: HttpRequest<T> | Rest.Request<T>, 
    config?: Rest.Config, 
    api?: string
  ): Observable<R> {
    // Build the URL
    let url = request.url;
    if (this.baseUrl && !url.startsWith('http')) {
      url = this.baseUrl + url;
    }

    // Build headers
    let headers = new HttpHeaders();
    
    // Add default headers
    if (!config?.skipAddingHeader) {
      headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Accept', 'application/json');
    }

    // Add custom headers from request
    if (request.headers) {
      if (request.headers instanceof HttpHeaders) {
        request.headers.keys().forEach(key => {
          const values = request.headers!.getAll(key);
          if (values) {
            values.forEach(value => {
              headers = headers.append(key, value);
            });
          }
        });
      } else {
        Object.keys(request.headers).forEach(key => {
          const value = (request.headers as any)[key];
          if (Array.isArray(value)) {
            value.forEach(v => headers = headers.append(key, v));
          } else {
            headers = headers.set(key, value);
          }
        });
      }
    }

    // Build query parameters
    let params = new HttpParams({ encoder: config?.httpParamEncoder });
    if (request.params) {
      if (request.params instanceof HttpParams) {
        params = request.params;
      } else {
        Object.keys(request.params).forEach(key => {
          const value = request.params![key];
          if (value !== null && value !== undefined) {
            if (Array.isArray(value)) {
              value.forEach(v => params = params.append(key, v.toString()));
            } else {
              params = params.set(key, value.toString());
            }
          }
        });
      }
    }

    // Build HTTP options
    const options: any = {
      headers,
      params,
      observe: config?.observe || 'body',
      reportProgress: request.reportProgress || false,
      responseType: request.responseType || 'json',
      withCredentials: request.withCredentials || false
    };

    // Add body for methods that support it
    if (request.method !== 'GET' && request.method !== 'DELETE' && request.method !== 'HEAD') {
      options.body = request.body;
    }

    // Make the HTTP request
    const httpRequest = this.http.request<R>(request.method, url, options);

    // Handle errors if not skipped
    if (config?.skipHandleError) {
      return httpRequest;
    } else {
      return httpRequest.pipe(
        catchError(err => this.handleError(err))
      );
    }
  }
}
