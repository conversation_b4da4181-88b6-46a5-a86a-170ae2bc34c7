import type { ChannelDto, ChannelTreeDto } from './dtos/models';
import { RestService, Rest } from '../../../../abp';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class ReadOnlyChannelService {
  apiName = 'Default';

  constructor(private restService: RestService) {}

  getChannelTree = (languageCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelTreeDto[]>({
      method: 'GET',
      url: '/api/app/read-only-channel/channel-tree',
      params: { languageCode },
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannel = (channelId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/read-only-channel/matched-channel/${channelId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByAlbumId = (albumId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/read-only-channel/matched-channel-by-album-id/${albumId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByBookId = (bookId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/read-only-channel/matched-channel-by-book-id/${bookId}`,
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByContentCode = (contentCode: string, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: '/api/app/read-only-channel/matched-channel-by-content-code',
      params: { contentCode },
    },
    { apiName: this.apiName,...config });
  

  getMatchedChannelByVirtualFolderId = (virtualFolderId: number, config?: Partial<Rest.Config>) =>
    this.restService.request<any, ChannelDto>({
      method: 'GET',
      url: `/api/app/read-only-channel/matched-channel-by-virtual-folder-id/${virtualFolderId}`,
    },
    { apiName: this.apiName,...config });
}
